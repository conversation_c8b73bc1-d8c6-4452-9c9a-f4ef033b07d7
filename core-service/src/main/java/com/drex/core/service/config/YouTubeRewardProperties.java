package com.drex.core.service.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * YouTube奖励配置属性类
 * 从application.properties读取配置
 * 支持长视频、短视频、X平台互动的差异化奖励配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "youtube.reward")
public class YouTubeRewardProperties {

    /**
     * 长视频奖励配置
     */
    private VideoRewardConfig longVideo = new VideoRewardConfig();

    /**
     * 短视频奖励配置
     */
    private VideoRewardConfig shortVideo = new VideoRewardConfig();

    /**
     * X平台互动奖励配置
     */
    private XPlatformRewardConfig xPlatform = new XPlatformRewardConfig();

    /**
     * 全局配置
     */
    private GlobalConfig global = new GlobalConfig();

    /**
     * 视频奖励配置
     */
    @Data
    public static class VideoRewardConfig {
        /**
         * 奖励阶段列表
         */
        private List<ProgressStage> stages = new ArrayList<>();

        /**
         * 视频类型（LONG/SHORT）
         */
        private String videoType;

        /**
         * 是否启用
         */
        private Boolean enabled = true;

        /**
         * 视频时长阈值（秒），用于区分长短视频
         */
        private Integer durationThresholdSeconds = 300; // 5分钟
    }

    /**
     * X平台奖励配置
     */
    @Data
    public static class XPlatformRewardConfig {
        /**
         * 发帖奖励配置
         */
        private XRewardStage postReward = new XRewardStage();

        /**
         * 回复奖励配置
         */
        private XRewardStage replyReward = new XRewardStage();

        /**
         * 是否启用
         */
        private Boolean enabled = true;
    }

    /**
     * 奖励阶段配置
     */
    @Data
    public static class ProgressStage {
        /**
         * 阶段编号
         */
        private Integer stage;

        /**
         * 阶段名称
         */
        private String stageName;

        /**
         * 最小观看百分比
         */
        private Double minWatchPercentage = 0.0;

        /**
         * 最大观看百分比
         */
        private Double maxWatchPercentage = 1.0;

        /**
         * 奖励金额
         */
        private Long rewardAmount = 0L;

        /**
         * 奖励等级
         */
        private String rewardLevel = "GOLD";

        /**
         * 是否启用
         */
        private Boolean enabled = true;
    }

    /**
     * X平台奖励阶段配置
     */
    @Data
    public static class XRewardStage {
        /**
         * 奖励金额
         */
        private Long rewardAmount = 0L;

        /**
         * 奖励等级
         */
        private String rewardLevel = "GOLD";

        /**
         * 是否启用
         */
        private Boolean enabled = true;

        /**
         * 每日最大奖励次数
         */
        private Integer maxDailyRewards = 10;
    }

    /**
     * 全局配置
     */
    @Data
    public static class GlobalConfig {
        /**
         * 最小会话时长（秒）
         */
        private Integer minSessionDurationSeconds = 60;

        /**
         * 最大会话时长（秒）
         */
        private Integer maxSessionDurationSeconds = 7200;

        /**
         * 是否启用实时奖励
         */
        private Boolean enableRealtimeReward = true;

        /**
         * 心跳间隔（秒）
         */
        private Integer heartbeatIntervalSeconds = 30;

        /**
         * 事件上报间隔（秒）
         */
        private Integer eventReportIntervalSeconds = 10;

        /**
         * 最大并发会话数
         */
        private Integer maxConcurrentSessions = 5;

        /**
         * 风险分数阈值（分数越高风险越高，超过阈值不发放奖励）
         */
        private Integer riskScoreThreshold = 95;

        /**
         * 短视频时长阈值（秒），小于等于此值的视频被认为是短视频
         */
        private Integer shortVideoThresholdSeconds = 300; // 5分钟
    }
    /**
     * 根据视频时长判断是长视频还是短视频
     */
    public VideoRewardConfig getVideoRewardConfig(Long videoDurationSeconds) {
        if (videoDurationSeconds == null) {
            return longVideo; // 默认返回长视频配置
        }

        if (videoDurationSeconds <= global.getShortVideoThresholdSeconds()) {
            return shortVideo;
        } else {
            return longVideo;
        }
    }

    /**
     * 根据观看百分比和视频类型计算进度等级
     */
    public Integer calculateProgress(Double watchPercentage, Long videoDurationSeconds) {
        if (watchPercentage == null) {
            return null;
        }

        VideoRewardConfig config = getVideoRewardConfig(videoDurationSeconds);

        for (ProgressStage stage : config.getStages()) {
            if (stage.getEnabled() &&
                    watchPercentage >= stage.getMinWatchPercentage() &&
                    watchPercentage <= stage.getMaxWatchPercentage()) {
                return stage.getStage();
            }
        }

        return null;
    }

    /**
     * 根据阶段获取奖励配置
     */
    public ProgressStage getProgressStage(Integer stage, Long videoDurationSeconds) {
        VideoRewardConfig config = getVideoRewardConfig(videoDurationSeconds);

        return config.getStages().stream()
                .filter(s -> s.getStage().equals(stage) && s.getEnabled())
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取视频的总奖励阶段数
     */
    public Integer getTotalStages(Long videoDurationSeconds) {
        VideoRewardConfig config = getVideoRewardConfig(videoDurationSeconds);
        return (int) config.getStages().stream()
                .filter(ProgressStage::getEnabled)
                .count();
    }

    /**
     * 验证配置有效性
     */
    public boolean isValid() {
        return validateVideoRewardConfig(longVideo) &&
                validateVideoRewardConfig(shortVideo) &&
                validateXPlatformConfig(xPlatform);
    }

    /**
     * 验证视频奖励配置
     */
    private boolean validateVideoRewardConfig(VideoRewardConfig config) {
        if (config == null || config.getStages() == null) {
            return false;
        }

        for (ProgressStage stage : config.getStages()) {
            if (stage.getMinWatchPercentage() < 0 ||
                    stage.getMaxWatchPercentage() > 2.0 ||
                    stage.getMinWatchPercentage() >= stage.getMaxWatchPercentage() ||
                    stage.getRewardAmount() < 0) {
                return false;
            }
        }

        return true;
    }

    /**
     * 验证X平台配置
     */
    private boolean validateXPlatformConfig(XPlatformRewardConfig config) {
        if (config == null) {
            return false;
        }

        return validateXRewardStage(config.getPostReward()) &&
                validateXRewardStage(config.getReplyReward());
    }

    /**
     * 验证X平台奖励阶段
     */
    private boolean validateXRewardStage(XRewardStage stage) {
        return stage != null &&
                stage.getRewardAmount() >= 0 &&
                stage.getMaxDailyRewards() > 0;
    }
}